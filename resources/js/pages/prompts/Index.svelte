<script lang="ts">
    import AppLayout from '@/layouts/AppLayout.svelte';
    import { Button } from '@/components/ui/button';
    import { Input } from '@/components/ui/input';
    import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
    import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
    import PromptCard from '@/components/prompts/PromptCard.svelte';
    import { Plus, Search, RefreshCw } from 'lucide-svelte';
    import { api } from '@/lib/api';
    import type { Prompt, PromptFilters, PaginatedResponse, BreadcrumbItem } from '@/types';
    import { onMount } from 'svelte';

    // State
    let prompts: Prompt[] = $state([]);
    let loading = $state(true);
    let error = $state<string | null>(null);
    let pagination = $state<PaginatedResponse<Prompt> | null>(null);
    
    // Filters
    let filters: PromptFilters = $state({
        search: '',
        status: '',
        model: ''
    });
    
    let currentPage = $state(1);

    const breadcrumbs: BreadcrumbItem[] = [
        { title: 'Dashboard', href: '/dashboard' },
        { title: 'Prompts', href: '/prompts' }
    ];

    async function loadPrompts(page = 1) {
        try {
            loading = true;
            error = null;
            
            const response = await api.getPrompts(filters, page);
            prompts = response.data;
            pagination = response;
            currentPage = page;
        } catch (err) {
            error = err instanceof Error ? err.message : 'Failed to load prompts';
            console.error('Error loading prompts:', err);
        } finally {
            loading = false;
        }
    }

    function handleSearch() {
        currentPage = 1;
        loadPrompts(1);
    }

    function handleFilterChange() {
        currentPage = 1;
        loadPrompts(1);
    }

    function clearFilters() {
        filters = { search: '', status: '', model: '' };
        handleFilterChange();
    }

    onMount(() => {
        loadPrompts();
    });
</script>

<svelte:head>
    <title>Prompts - QueueAI</title>
</svelte:head>

<AppLayout {breadcrumbs}>
    <div class="space-y-6 p-6">
        <!-- Header -->
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-semibold tracking-tight">Prompts</h1>
                <p class="text-muted-foreground">
                    Manage and monitor your AI prompts and responses
                </p>
            </div>
            <Button href="/prompts/create">
                <Plus class="mr-2 h-4 w-4" />
                New Prompt
            </Button>
        </div>

        <!-- Filters -->
        <Card>
            <CardHeader>
                <CardTitle class="text-base">Filters</CardTitle>
                <CardDescription>Search and filter your prompts</CardDescription>
            </CardHeader>
            <CardContent>
                <div class="flex flex-col gap-4 md:flex-row md:items-end">
                    <div class="flex-1">
                        <label for="search" class="text-sm font-medium">Search</label>
                        <div class="relative">
                            <Search class="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                            <Input
                                id="search"
                                placeholder="Search prompts..."
                                bind:value={filters.search}
                                class="pl-9"
                                onkeydown={(e) => e.key === 'Enter' && handleSearch()}
                            />
                        </div>
                    </div>
                    
                    <div class="w-full md:w-48">
                        <label for="status-select" class="text-sm font-medium">Status</label>
                        <Select bind:value={filters.status} onValueChange={handleFilterChange}>
                            <SelectTrigger id="status-select">
                                <SelectValue placeholder="All statuses" />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="">All statuses</SelectItem>
                                <SelectItem value="pending">Pending</SelectItem>
                                <SelectItem value="processing">Processing</SelectItem>
                                <SelectItem value="completed">Completed</SelectItem>
                                <SelectItem value="failed">Failed</SelectItem>
                            </SelectContent>
                        </Select>
                    </div>
                    
                    <div class="flex gap-2">
                        <Button variant="outline" onclick={handleSearch}>
                            <Search class="mr-2 h-4 w-4" />
                            Search
                        </Button>
                        <Button variant="outline" onclick={clearFilters}>
                            Clear
                        </Button>
                        <Button variant="outline" onclick={() => loadPrompts(currentPage)}>
                            <RefreshCw class="h-4 w-4" />
                        </Button>
                    </div>
                </div>
            </CardContent>
        </Card>

        <!-- Content -->
        {#if loading}
            <div class="flex items-center justify-center py-12">
                <RefreshCw class="h-6 w-6 animate-spin mr-2" />
                <span>Loading prompts...</span>
            </div>
        {:else if error}
            <Card>
                <CardContent class="py-12">
                    <div class="text-center">
                        <p class="text-red-600 mb-4">{error}</p>
                        <Button variant="outline" onclick={() => loadPrompts(currentPage)}>
                            <RefreshCw class="mr-2 h-4 w-4" />
                            Try Again
                        </Button>
                    </div>
                </CardContent>
            </Card>
        {:else if prompts.length === 0}
            <Card>
                <CardContent class="py-12">
                    <div class="text-center">
                        <p class="text-muted-foreground mb-4">No prompts found</p>
                        <Button href="/prompts/create">
                            <Plus class="mr-2 h-4 w-4" />
                            Create your first prompt
                        </Button>
                    </div>
                </CardContent>
            </Card>
        {:else}
            <!-- Prompts Grid -->
            <div class="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                {#each prompts as prompt (prompt.uuid)}
                    <PromptCard {prompt} />
                {/each}
            </div>

            <!-- Pagination -->
            {#if pagination && pagination.last_page > 1}
                <div class="flex items-center justify-center gap-2">
                    <Button
                        variant="outline"
                        disabled={currentPage <= 1}
                        onclick={() => loadPrompts(currentPage - 1)}
                    >
                        Previous
                    </Button>

                    <span class="text-sm text-muted-foreground">
                        Page {currentPage} of {pagination.last_page}
                    </span>

                    <Button
                        variant="outline"
                        disabled={currentPage >= pagination.last_page}
                        onclick={() => loadPrompts(currentPage + 1)}
                    >
                        Next
                    </Button>
                </div>
            {/if}
        {/if}
    </div>
</AppLayout>
