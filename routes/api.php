<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\PromptController;
use App\Http\Controllers\SystemController;
use App\Http\Controllers\SubjectController;

Route::get('/user', function (Request $request) {
    return $request->user();
})->middleware('auth:sanctum');

// Rotas do sistema
Route::prefix('system')->group(function () {
    Route::get('/status', [SystemController::class, 'status'])->name('system.status');
    Route::get('/models', [SystemController::class, 'models'])->name('system.models');
    Route::get('/stats', [SystemController::class, 'stats'])->name('system.stats');
});

// Rotas para o sistema de prompts
Route::prefix('prompts')->middleware('auth:sanctum')->group(function () {
    Route::get('/', [PromptController::class, 'index'])->name('prompts.index');
    Route::post('/', [PromptController::class, 'store'])->name('prompts.store');
    Route::get('/{prompt}', [PromptController::class, 'show'])->name('prompts.show');
    Route::get('/{prompt}/status', [PromptController::class, 'status'])->name('prompts.status');
    Route::get('/{prompt}/responses', [PromptController::class, 'responses'])->name('prompts.responses');
});

// Rotas para assuntos (requer autenticação)
Route::middleware('auth:sanctum')->prefix('subjects')->group(function () {
    // Rotas RESTful básicas
    Route::get('/', [SubjectController::class, 'index'])->name('subjects.index');
    Route::post('/', [SubjectController::class, 'store'])->name('subjects.store');
    Route::get('/{subject}', [SubjectController::class, 'show'])->name('subjects.show');
    Route::put('/{subject}', [SubjectController::class, 'update'])->name('subjects.update');
    Route::patch('/{subject}', [SubjectController::class, 'update'])->name('subjects.patch');
    Route::delete('/{subject}', [SubjectController::class, 'destroy'])->name('subjects.destroy');

    // Rotas específicas para estrutura de árvore
    Route::get('/roots/list', [SubjectController::class, 'roots'])->name('subjects.roots');
    Route::get('/{subject}/children', [SubjectController::class, 'children'])->name('subjects.children');
});
